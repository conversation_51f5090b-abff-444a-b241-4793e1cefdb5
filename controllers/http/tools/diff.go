package tools

import (
	"fmt"
	"fwyytool/api/arkgo"
	"fwyytool/api/userprofile"
	"fwyytool/components"
	"fwyytool/consts"
	"fwyytool/libs/utils"
	"fwyytool/service/tools/diffJob"
	"fwyytool/stru"
	"net/http"
	"strconv"
	"time"

	"git.zuoyebang.cc/pkg/golib/v2/base"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
)

var DiffController diffController

type diffController struct {
}

func (s diffController) DoDiff(ctx *gin.Context) {
	err := diffJob.DiffJobService.Do(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, "success")
}

func (s diffController) DiffDetail(ctx *gin.Context) {
	ctx.HTML(http.StatusOK, "desk/datadiff/diffdetail.html", nil)
}

// GetDiffCount 获取差异统计数据
func (s diffController) GetDiffCount(ctx *gin.Context) {
	var param arkgo.GetArkStudentDataDiffResParam
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, errors.Wrap(components.ErrorParamInvalid, err.Error()))
		return
	}

	// 默认查询有差异的记录
	if param.HasDiff == nil {
		defaultHasDiff := 1
		param.HasDiff = &defaultHasDiff
	}

	resp, err := arkgo.NewClient().GetDiffCountRes(ctx, param)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, resp)
}

// GetDiffOverview 获取差异总览数据
func (s diffController) GetDiffOverview(ctx *gin.Context) {
	var param arkgo.GetArkStudentDataDiffResParam
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, errors.Wrap(components.ErrorParamInvalid, err.Error()))
		return
	}

	resp, err := arkgo.NewClient().GetDiffOverview(ctx, param)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, resp)
}

// GetDiffRes 获取差异结果数据
func (s diffController) GetDiffRes(ctx *gin.Context) {
	var param arkgo.GetArkStudentDataDiffResParam
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, errors.Wrap(components.ErrorParamInvalid, err.Error()))
		return
	}

	resp, err := arkgo.NewClient().GetArkStudentDataDiffRes(ctx, param)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, resp)
}

// GetDiffConfig 获取Diff控制配置
func (s diffController) GetDiffConfig(ctx *gin.Context) {
	diffConfig, err := diffJob.GetDiffConfig(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, diffConfig)
}

// UpdateDiffConfig 保存Diff控制配置
func (s diffController) UpdateDiffConfig(ctx *gin.Context) {
	var req struct {
		Config string `json:"config" binding:"required"`
	}

	if err := ctx.ShouldBind(&req); err != nil {
		base.RenderJsonFail(ctx, errors.Wrap(components.ErrorParamInvalid, err.Error()))
		return
	}

	user, _ := ctx.Get(consts.LOGIN_USER_INFO)
	userInfo := user.(*userprofile.UserInfo)

	err := arkgo.NewClient().UpdateRdConfig(ctx, arkgo.UpdateRdConfigParams{
		Key:            "assistantdesk_ark_student_list_data_diff_control_config",
		Value:          req.Config,
		UpdateStaffUid: int64(userInfo.UserId),
	})
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, "success")
}

// GetDiffHandlerNames 获取差异接口列表
func (s diffController) GetDiffHandlerNames(ctx *gin.Context) {
	var param arkgo.GetDiffHandlerNamesParam
	if err := ctx.ShouldBind(&param); err != nil {
		base.RenderJsonFail(ctx, errors.Wrap(components.ErrorParamInvalid, err.Error()))
		return
	}

	resp, err := arkgo.NewClient().GetDiffHandlerNames(ctx, param)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, resp)
}

// ExportDiffData 批量导出差异数据
func (s diffController) ExportDiffData(ctx *gin.Context) {
	// 获取选中的ID数组
	ids := ctx.QueryArray("ids")
	if len(ids) == 0 {
		base.RenderJsonFail(ctx, errors.Wrap(components.ErrorParamInvalid, "ids参数不能为空"))
		return
	}

	// 将字符串ID转换为int64数组
	var idInt64s []int64
	for _, idStr := range ids {
		if id, err := strconv.ParseInt(idStr, 10, 64); err == nil {
			idInt64s = append(idInt64s, id)
		}
	}

	if len(idInt64s) == 0 {
		base.RenderJsonFail(ctx, errors.Wrap(components.ErrorParamInvalid, "有效的ID不能为空"))
		return
	}

	// 根据ID数组查询具体的差异数据
	records, err := arkgo.NewClient().GetDiffRecordsByIds(ctx, arkgo.GetDiffRecordsByIdsParams{IDs: idInt64s})
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	var exportData []stru.DiffExportItem
	for _, record := range records {
		exportData = append(exportData, stru.DiffExportItem{
			HandlerName: record.HandlerName,
			OldData:     record.OldData,
			NewData:     record.NewData,
		})
	}

	// 设置响应头为JSON文件下载
	ctx.Header("Content-Type", "application/json")
	ctx.Header("Content-Disposition", fmt.Sprintf("attachment; filename=diff_analysis_%d.json", time.Now().Unix()))

	ctx.JSON(http.StatusOK, exportData)
}

// RerunDiff 重跑指定的 diff 记录
func (s diffController) RerunDiff(ctx *gin.Context) {
	var req struct {
		IDs []int64 `json:"ids" binding:"required"`
	}

	if err := ctx.ShouldBind(&req); err != nil {
		base.RenderJsonFail(ctx, errors.Wrap(components.ErrorParamInvalid, err.Error()))
		return
	}

	// 获取配置
	diffConfig, err := diffJob.GetDiffConfig(ctx)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	// 获取记录信息
	records, err := arkgo.NewClient().GetDiffRecordsByIds(ctx, arkgo.GetDiffRecordsByIdsParams{IDs: req.IDs})
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	if len(records) != len(req.IDs) {
		base.RenderJsonFail(ctx, errors.Wrap(components.ErrorParamInvalid, "有记录不存在，请检查"))
		return
	}

	// 批量更新状态
	err = arkgo.NewClient().UpdateDiffRecords(ctx, arkgo.UpdateDiffRecordsParams{
		IDs: req.IDs,
		Updates: map[string]interface{}{
			"status": 0,
		},
	})
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
	}

	// 启动异步处理
	executor := utils.NewConcurrentExecutor(ctx, 10, func(ctx *gin.Context, record stru.ArkStudentListDataDiff) error {
		err := diffJob.HandleOneTask(ctx, record, diffConfig)
		if err != nil {
			zlog.Errorf(ctx, "Async diff task failed, task_id: %d, error: %v", record.ID, err)
			// 更新状态为失败
			updateErr := arkgo.NewClient().UpdateDiffRecords(ctx, arkgo.UpdateDiffRecordsParams{
				IDs: []int64{record.ID},
				Updates: map[string]interface{}{
					"status": consts.ArkStudentListDataDiffStatusFailed,
				},
			})
			if updateErr != nil {
				zlog.Errorf(ctx, "Failed to update status after async task failure, task_id: %d, error: %v", record.ID, updateErr)
			}
		} else {
			zlog.Info(ctx, "Async diff task completed successfully, task_id: %d", record.ID)
		}
		return nil
	})

	err = executor.Execute(records)
	if err != nil {
		base.RenderJsonFail(ctx, components.SystemErr(err.Error()))
		return
	}

	base.RenderJsonSucc(ctx, "success")
}
